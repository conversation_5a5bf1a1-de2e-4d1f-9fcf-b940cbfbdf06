@import '../../../assets/scss/variables.scss';

.sidebar-container {
    padding: 10px;
    border-radius: 10px;
    display: flex;
}

#sidebar {
    width: 250px;
    min-width: 60px; /* Ensure minimum width when collapsed */
    overflow-y: auto;
    color: #333333;
    transition: width 0.3s ease-in-out;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;
    text-align: left;
    border-radius: 12px;
    flex-shrink: 0; /* Prevent sidebar from shrinking */
    background: #ffffff;
    padding: 20px 0;

    /* Hide scrollbar but keep functionality */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    /* Hide scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
        display: none;
    }
}

/* Collapsed sidebar */
#sidebar.collapsed {
    width: 60px;
    padding: 20px 0;
}

/* Title text in navigation items */
#sidebar .nav-title {
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
    opacity: 1;
    visibility: visible;
    white-space: nowrap;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
}

/* Hide text and submenu icons when collapsed */
#sidebar.collapsed .nav-title {
    opacity: 0;
    visibility: hidden;
    width: 0;
    margin: 0;
    padding: 0;
}

#sidebar.collapsed .submenu-icon {
    display: none;
}

/* Hide submenus when collapsed */
#sidebar.collapsed .submenu {
    display: none;
}

/* Menu header */
.menu-header {
    padding: 0 20px 15px 20px;
    font-size: 12px;
    font-weight: 500;
    color: #999999;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}

/* List Items */
#sidebar .list-group-item,
#sidebar .nav-link {
    background: transparent;
    border: none;
    color: #333333;
    padding: 12px 20px;
    transition: all 0.2s ease-in-out;
    font-size: 14px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 12px;
    overflow: hidden;
    position: relative;
    margin: 2px 8px;
    border-radius: 8px;
}

/* Hover state for list items */
#sidebar .list-group-item:hover {
    background-color: #f8f9fa;
    color: #333333;
}

/* Active state for list items */
#sidebar .list-group-item.active {
    background: transparent;
    color: #2D336B;
    font-weight: 500;
    border-left: 4px solid #2D336B;
}

/* Parent items with active children */
#sidebar .list-group-item.active .iconStyle {
    color: #2D336B;
}

/* Enhanced styling for parent items with active children */
#sidebar .list-group-item.parent-of-active {
    background-color: transparent;
    color: #2D336B;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    border-left: 4px solid #2D336B;
}

#sidebar .list-group-item.parent-of-active .iconStyle {
    color: #2D336B;
}

.active-indicator {
    width: 4px;
    height: 100%;
    background: #2D336B;
    border-radius: 0;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
}

.iconStyle {
    margin-right: 0;
    font-size: 18px;
    min-width: 20px;
    text-align: center;
    transition: all 0.2s ease-in-out;
    color: #666666;
}

/* Active icon styling */
#sidebar .list-group-item.active .iconStyle {
    color: #2D336B;
}

/* Parent active icon styling */
#sidebar .list-group-item.parent-of-active .iconStyle {
    color: #2D336B;
}

/* Center icons when sidebar is collapsed */
#sidebar.collapsed .iconStyle {
    margin: 0 auto;
}

/* Navigation item content container */
.nav-item-content {
    width: 100%;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    gap: 12px;
}

#sidebar.collapsed .nav-item-content {
    justify-content: center;
    gap: 0;
}

/* Special styling for menu items with subitems when collapsed */
#sidebar.collapsed .list-group-item {
    position: relative;
    justify-content: center;
}

#sidebar.collapsed .list-group-item:hover {
    background-color: #f8f9fa;
}

/* Submenu icon styling */
.submenu-icon {
    font-size: 12px;
    color: #999999;
    transition: all 0.2s ease-in-out;
}

#sidebar .list-group-item.active .submenu-icon {
    color: #2D336B;
}

#sidebar .list-group-item.parent-of-active .submenu-icon {
    color: #2D336B;
}

/* Submenu styling */
.submenu {
    overflow: hidden;
    margin-top: 4px;

    .list-group-item {
        margin-left: 32px;
        margin-right: 8px;
        font-size: 13px;
        padding: 10px 20px;
        color: #666666;
        background: transparent;

        &:hover {
            background-color: #f8f9fa;
            color: #333333;
        }

        &.active {
            background: #e8f0fe;
            color: #2D336B;
            font-weight: 500;
        }

        .iconStyle {
            font-size: 16px;
            color: #999999;
        }

        &.active .iconStyle {
            color: #2D336B;
        }
    }
}

/* Content margin adjustment */
.content-wrapper {
    margin-top: -12px;
}

/* Collapsed sidebar menu header */
#sidebar.collapsed .menu-header {
    display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #sidebar {
        width: 100%;
        position: fixed;
        height: 100vh;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    #sidebar:not(.collapsed) {
        transform: translateX(0);
    }

    .sidebar-container {
        padding: 0;
    }

    .content-wrapper {
        margin-left: 0;
        width: 100%;
    }
}

/* Smooth transitions for all interactive elements */
#sidebar .list-group-item,
#sidebar .iconStyle,
#sidebar .nav-title,
#sidebar .submenu-icon {
    transition: all 0.2s ease-in-out;
}

/* Focus states for accessibility */
#sidebar .list-group-item:focus {
    outline: 2px solid #2D336B;
    outline-offset: 2px;
}

/* Ensure proper spacing between menu sections */
#sidebar .list-group-item:not(:last-child) {
    margin-bottom: 2px;
}